import { Provide, InjectClient, Config, Inject, Context } from '@midwayjs/core';
import {
  HttpServiceFactory,
  HttpService,
  AxiosResponse,
} from '@midwayjs/axios';
import { WeappListener } from '../listener/weapp';
import * as crypto from 'crypto';
import path = require('path');
import { WechatSubscription } from '../entity/wechat-subscription.entity';
import { Order } from '../entity/order.entity';
import {
  orderConfirmationMessage,
  orderStatusChangeMessage,
  orderTimeChangeMessage,
  orderCompletionMessage,
} from '../common/MessageTemplate';
import { CustomError } from '../error/custom.error';
import { Employee, OrderDetail, Pet, Service } from '../entity';

/** 消息类型枚举 */
export enum MessageType {
  ORDER_CONFIRMATION = 'orderConfirmation', // 接单成功提醒
  ORDER_STATUS_CHANGE = 'orderStatusChange', // 服务进度提醒
  ORDER_TIME_CHANGE = 'orderTimeChange', // 服务时间更改通知
  ORDER_COMPLETION = 'orderCompletion', // 服务完成通知
}

@Provide()
export class WeappService {
  @Inject()
  ctx: Context;

  @Inject()
  weappListener: WeappListener;

  @InjectClient(HttpServiceFactory, 'weapp')
  weapp: HttpService;

  @Config('weapp')
  weappConfig: {
    appid: string;
    secret: string;
    sym_sn: string;
    sym_key: string;
    templateIds: Record<MessageType, string>;
  };

  // 证书私钥
  sym_key = require('fs').readFileSync(
    path.resolve(__dirname, '../cert/apiclient_key.pem'),
    'utf8'
  );

  pay_sign({
    appid,
    timeStamp,
    nonceStr,
    perpayId,
  }: {
    appid: string;
    timeStamp: string;
    nonceStr: string;
    perpayId: string;
  }) {
    // const timestamp = Math.floor(Date.now() / 1000);
    // const nonce_str = crypto.randomBytes(16).toString('hex');
    const message = `${appid}\n${timeStamp}\n${nonceStr}\n${perpayId}\n`;
    // 使用SHA256 with RSA算法进行签名，并进行Base64编码
    const signature = crypto
      .createSign('RSA-SHA256')
      .update(message)
      .sign(this.sym_key, 'base64');

    return signature;
  }

  private analysisRes(res: AxiosResponse<any, any>) {
    if (res.status !== 200) {
      this.ctx.logger.error(res.data);
      console.log(res.data);
      throw new Error('小程序接口调用失败');
    }
    const { errcode, errmsg, ...data } = res.data;
    if (errcode) {
      this.ctx.logger.error(res.data);
      console.log(res.data);
      throw new Error(errmsg);
    }
    return data;
  }

  // private getNewReq(queryInfo: Record<string, any>) {
  //   const local_ts = Math.floor(Date.now() / 1000); //加密签名使用的统一时间戳
  //   const nonce = crypto.randomBytes(16).toString('base64').replace(/=/g, '');

  //   const reqex = {
  //     _n: nonce,
  //     _appid: this.weappConfig.appid,
  //     _timestamp: local_ts,
  //   };

  //   const real_req = Object.assign({}, reqex, queryInfo); // 生成并添加安全校验字段
  //   const plaintext = JSON.stringify(real_req);

  //   // const aad = `${url_path}|${local_appid}|${local_ts}|${local_sym_sn}`;

  //   const real_key = Buffer.from(this.weappConfig.sym_key, 'base64');
  //   const real_iv = crypto.randomBytes(12);
  //   // const real_aad = Buffer.from(aad, 'utf-8');
  //   const real_plaintext = Buffer.from(plaintext, 'utf-8');

  //   const cipher = crypto.createCipheriv('aes-256-gcm', real_key, real_iv);
  //   // cipher.setAAD(real_aad);

  //   const cipher_update = cipher.update(real_plaintext);
  //   const cipher_final = cipher.final();
  //   const real_ciphertext = Buffer.concat([cipher_update, cipher_final]);
  //   const real_authTag = cipher.getAuthTag();

  //   const iv = real_iv.toString('base64');
  //   const data = real_ciphertext.toString('base64');
  //   const authtag = real_authTag.toString('base64');

  //   const req_data = {
  //     iv,
  //     data,
  //     authtag,
  //   };

  //   const new_req = {
  //     req_ts: local_ts,
  //     req_data: JSON.stringify(req_data),
  //   };
  //   return new_req;
  // }

  async code2Session(code: string) {
    const params: Record<string, string> = {
      appid: this.weappConfig.appid,
      secret: this.weappConfig.secret,
      js_code: code,
      grant_type: 'authorization_code',
    };
    // console.log(params);
    const res = await this.weapp.get('/sns/jscode2session', {
      params,
    });

    return this.analysisRes(res);
  }

  /** 获取用户手机号 */
  async getPhoneNumber(code: string, isMember: boolean | string) {
    const { token_weapp, token_weapp_employee } = this.weappListener.getData();
    const token = !isMember ? token_weapp : token_weapp_employee;
    console.log(token);
    const res = await this.weapp.post(
      '/wxa/business/getuserphonenumber?access_token=' + token,
      {
        code,
      }
    );
    console.log(res.data);
    return this.analysisRes(res);
  }

  /** 保存用户订阅状态 */
  async saveSubscription(
    openId: string,
    templateId: string,
    orderId: string,
    status: boolean
  ) {
    const subscription = await WechatSubscription.findOne({
      where: { openId, templateId, orderId },
    });

    const info = {
      status,
      subscribeTime: status ? new Date() : null,
      unsubscribeTime: status ? null : new Date(),
    };
    if (subscription) {
      subscription.status = info.status;
      subscription.subscribeTime = info.subscribeTime;
      subscription.unsubscribeTime = info.unsubscribeTime;
      return subscription.save();
    } else {
      const newSubscription = WechatSubscription.create({
        openId,
        templateId,
        orderId,
        ...info,
      });
      return newSubscription;
    }
  }

  /** 通用发送订单消息方法 */
  async sendOrderMessage(
    sn: string,
    messageType: MessageType,
    extraParams?: { msg?: string }
  ) {
    const { token_weapp } = this.weappListener.getData();

    // 获取订单信息
    const order = await Order.findOne({
      where: { sn },
      include: [
        Employee,
        {
          model: OrderDetail,
          include: [Service, Pet],
        },
      ],
    });
    if (!order) {
      throw new CustomError(`订单 ${sn} 不存在`);
    }

    // 获取订阅该订单的用户
    const subscription = await WechatSubscription.findOne({
      where: {
        orderId: order.id,
        templateId: this.weappConfig.templateIds[messageType],
        status: true,
      },
    });

    if (!subscription) {
      throw new CustomError('没有订阅该订单的用户');
    }

    // 根据消息类型生成消息数据
    let messageData: any;
    switch (messageType) {
      case MessageType.ORDER_CONFIRMATION:
        messageData = orderConfirmationMessage(order);
        break;
      case MessageType.ORDER_STATUS_CHANGE:
        messageData = orderStatusChangeMessage(order, extraParams?.msg || '');
        break;
      case MessageType.ORDER_TIME_CHANGE:
        messageData = orderTimeChangeMessage(order);
        break;
      case MessageType.ORDER_COMPLETION:
        messageData = orderCompletionMessage(order);
        break;
      default:
        throw new CustomError(`不支持的消息类型: ${messageType}`);
    }

    // 准备发送消息
    let successCount = 0;
    let failedCount = 0;

    console.log('messageData: ', messageData);
    try {
      const result = await this.sendTemplateMessage(
        token_weapp,
        subscription.openId,
        subscription.templateId,
        messageData
      );

      if (result.success) {
        successCount++;
        // 更新最后发送时间
        subscription.lastSendTime = new Date();
        await subscription.save();
      } else {
        failedCount++;
      }
    } catch (error) {
      console.error(error);
      failedCount++;
      this.ctx.logger.error(
        `发送消息【${subscription.templateId}】失败: ${error.message}`,
        error
      );
    }

    return { success: successCount, failed: failedCount };
  }

  /** 发送接单成功提醒消息 */
  async sendOrderConfirmationMessage(sn: string) {
    try {
      console.log('sendOrderConfirmationMessage: ', sn);
      return this.sendOrderMessage(sn, MessageType.ORDER_CONFIRMATION);
    } catch (error) {
      this.ctx.logger.error(`发送消息失败: ${error.message}`, error);
    }
  }

  /** 发送服务进度提醒消息 */
  async sendOrderStatusChangeMessage(sn: string, msg?: string) {
    try {
      console.log('sendOrderStatusChangeMessage: ', sn, msg);
      return this.sendOrderMessage(sn, MessageType.ORDER_STATUS_CHANGE, {
        msg,
      });
    } catch (error) {
      this.ctx.logger.error(`发送消息失败: ${error.message}`, error);
    }
  }

  /** 发送服务时间更改通知消息 */
  async sendOrderTimeChangeMessage(sn: string) {
    try {
      console.log('sendOrderTimeChangeMessage: ', sn);
      return this.sendOrderMessage(sn, MessageType.ORDER_TIME_CHANGE);
    } catch (error) {
      this.ctx.logger.error(`发送消息失败: ${error.message}`, error);
    }
  }

  /** 发送服务完成通知消息 */
  async sendOrderCompletionMessage(sn: string) {
    try {
      console.log('sendOrderCompletionMessage: ', sn);
      return this.sendOrderMessage(sn, MessageType.ORDER_COMPLETION);
    } catch (error) {
      this.ctx.logger.error(`发送消息失败: ${error.message}`, error);
    }
  }

  // 调用微信API发送模板消息
  private async sendTemplateMessage(
    accessToken: string,
    openId: string,
    templateId: string,
    messageData: any
  ) {
    const url = `/cgi-bin/message/subscribe/send?access_token=${accessToken}`;

    messageData.touser = openId;
    messageData.template_id = templateId;

    console.log('debugger messageData: ', messageData);
    const response = await this.weapp.post(url, messageData);
    const result = this.analysisRes(response);
    console.log(result);

    if (result.errcode === 0) {
      return { success: true, messageId: result.msgid };
    } else {
      this.ctx.logger.error(result);
      throw new CustomError(result.errmsg);
    }
  }
}
